{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/work/mustang-air-works/admin%20panel/mustang_admin_panel/src/components/sidebar/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { AlertCircleIcon, ApertureIcon, CardSimIcon, CircleDotIcon, LayoutDashboardIcon, Lock, Sparkle, TableIcon, UserPlusIcon } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\n\r\ninterface MenuItem {\r\n  header?: string;\r\n  title?: string;\r\n  icon?: React.ReactNode;\r\n  to?: string;\r\n  chip?: string;\r\n  BgColor?: string;\r\n  chipBgColor?: string;\r\n  chipColor?: string;\r\n  chipVariant?: string;\r\n  chipIcon?: string;\r\n  children?: MenuItem[];\r\n  disabled?: boolean;\r\n  type?: string;\r\n  subCaption?: string;\r\n}\r\n\r\nconst sidebarItem: MenuItem[] = [\r\n  { header: \"Home\" },\r\n  {\r\n    title: \"Dashboard\",\r\n    icon: <LayoutDashboardIcon size={20} className=\"iconClass\" />,\r\n    to: \"/\",\r\n  },\r\n  { header: \"ui\" },\r\n  {\r\n    title: \"Alert\",\r\n    icon: <AlertCircleIcon size={20} className=\"iconClass\" />,\r\n    to: \"/ui-components/alerts\",\r\n  },\r\n  {\r\n    title: \"Button\",\r\n    icon: <CircleDotIcon size={20} className=\"iconClass\" />,\r\n    to: \"/ui-components/buttons\",\r\n  },\r\n  {\r\n    title: \"Cards\",\r\n    icon: <CardSimIcon size={20} className=\"iconClass\" />,\r\n    to: \"/ui-components/cards\",\r\n  },\r\n  {\r\n    title: \"Tables\",\r\n    icon: <TableIcon size={20} className=\"iconClass\" />,\r\n    to: \"/ui-components/tables\",\r\n  },\r\n  { header: \"Auth\" },\r\n  {\r\n    title: \"Login\",\r\n    icon: <Lock size={20} className=\"iconClass\" />,\r\n    to: \"/auth/login\",\r\n  },\r\n  {\r\n    title: \"Register\",\r\n    icon: <UserPlusIcon size={20} className=\"iconClass\" />,\r\n    to: \"/auth/register\",\r\n  },\r\n  { header: \"Extra\" },\r\n  {\r\n    title: \"Icons\",\r\n    icon: <Sparkle size={20} className=\"iconClass\" />,\r\n    to: \"/pages/icons\",\r\n  },\r\n  {\r\n    title: \"Sample Page\",\r\n    icon: <ApertureIcon size={20} className=\"iconClass\" />,\r\n    to: \"/pages/sample-page\",\r\n  },\r\n];\r\n\r\nconst Sidebar: React.FC = () => {\r\n  const router = useRouter();\r\n\r\n  return (\r\n    <nav className=\"w-64 h-full bg-white shadow-md py-6\">\r\n      <ul>\r\n        {sidebarItem.map((item, idx) =>\r\n          item.header ? (\r\n            <li key={idx} className=\"px-4 py-2 text-xs font-semibold text-gray-500 uppercase mt-6\">\r\n              {item.header}\r\n            </li>\r\n          ) : (\r\n            <li key={idx}>\r\n              <Link href={item.to ?? \"#\"} passHref legacyBehavior>\r\n                <a\r\n                  className={`flex items-center px-4 py-2 text-gray-700 hover:bg-blue-50 rounded-lg transition duration-150\r\n                    ${router.pathname === item.to ? \"bg-blue-100 text-blue-600 font-semibold\" : \"\"}\r\n                    ${item.disabled ? \"opacity-50 pointer-events-none\" : \"\"}`}\r\n                >\r\n                  {item.icon}\r\n                  <span className=\"ml-3\">{item.title}</span>\r\n                  {/* You may add chips, subtitles, etc. here depending on needs */}\r\n                </a>\r\n              </Link>\r\n            </li>\r\n          )\r\n        )}\r\n      </ul>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAyBA,MAAM,cAA0B;IAC9B;QAAE,QAAQ;IAAO;IACjB;QACE,OAAO;QACP,oBAAM,8OAAC,uPAAmB;YAAC,MAAM;YAAI,WAAU;;;;;;QAC/C,IAAI;IACN;IACA;QAAE,QAAQ;IAAK;IACf;QACE,OAAO;QACP,oBAAM,8OAAC,2OAAe;YAAC,MAAM;YAAI,WAAU;;;;;;QAC3C,IAAI;IACN;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,qOAAa;YAAC,MAAM;YAAI,WAAU;;;;;;QACzC,IAAI;IACN;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,+NAAW;YAAC,MAAM;YAAI,WAAU;;;;;;QACvC,IAAI;IACN;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,qNAAS;YAAC,MAAM;YAAI,WAAU;;;;;;QACrC,IAAI;IACN;IACA;QAAE,QAAQ;IAAO;IACjB;QACE,OAAO;QACP,oBAAM,8OAAC,0MAAI;YAAC,MAAM;YAAI,WAAU;;;;;;QAChC,IAAI;IACN;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,kOAAY;YAAC,MAAM;YAAI,WAAU;;;;;;QACxC,IAAI;IACN;IACA;QAAE,QAAQ;IAAQ;IAClB;QACE,OAAO;QACP,oBAAM,8OAAC,mNAAO;YAAC,MAAM;YAAI,WAAU;;;;;;QACnC,IAAI;IACN;IACA;QACE,OAAO;QACP,oBAAM,8OAAC,8NAAY;YAAC,MAAM;YAAI,WAAU;;;;;;QACxC,IAAI;IACN;CACD;AAED,MAAM,UAAoB;IACxB,MAAM,SAAS,IAAA,+IAAS;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;sBACE,YAAY,GAAG,CAAC,CAAC,MAAM,MACtB,KAAK,MAAM,iBACT,8OAAC;oBAAa,WAAU;8BACrB,KAAK,MAAM;mBADL;;;;6EAIT,8OAAC;8BACC,cAAA,8OAAC,uKAAI;wBAAC,MAAM,KAAK,EAAE,IAAI;wBAAK,QAAQ;wBAAC,cAAc;kCACjD,cAAA,8OAAC;4BACC,WAAW,CAAC;oBACV,EAAE,OAAO,QAAQ,KAAK,KAAK,EAAE,GAAG,4CAA4C,GAAG;oBAC/E,EAAE,KAAK,QAAQ,GAAG,mCAAmC,IAAI;;gCAE1D,KAAK,IAAI;8CACV,8OAAC;oCAAK,WAAU;8CAAQ,KAAK,KAAK;;;;;;;;;;;;;;;;;mBAR/B;;;;;;;;;;;;;;;AAkBrB;uCAEe", "debugId": null}}]}